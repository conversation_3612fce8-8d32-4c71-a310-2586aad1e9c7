export interface SessionConfig {
    workingDirectory: string;
    days: number;
    hoursPerDay: number;
    dailyHours?: Record<string, number>;
    mode: 'simple' | 'advanced';
}

export interface Change {
    file: string;
    description: string;
    changes: LineChange[];
}

export interface LineChange {
    lineNumber: number;
    oldContent: string;
    newContent: string;
}

export interface FileNode {
    name: string;
    path: string;
    type: 'file' | 'directory';
    children?: FileNode[];
    selected?: boolean;
}

export interface ProjectStructure {
    files: string[];
    directories: string[];
    tree: FileNode;
}
