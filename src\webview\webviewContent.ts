import { Webview, Uri } from 'vscode';

export function getWebviewContent(webview: Webview, extensionUri: Uri): string {
  const toolkitUri = webview.asWebviewUri(
    Uri.joinPath(extensionUri, 'node_modules', '@vscode', 'webview-ui-toolkit', 'dist', 'toolkit.js')
  );

  // Note: Using inline styles instead of external CSS file

  return /* html */ `<!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script type="module" src="${toolkitUri}"></script>
    <title>Wakatimer Pro</title>
    <style>
      :root {
        --primary-color: #007acc;
        --secondary-color: #1e1e1e;
        --background-color: var(--vscode-editor-background);
        --foreground-color: var(--vscode-editor-foreground);
        --border-color: var(--vscode-widget-border);
      }

      * {
        box-sizing: border-box;
      }

      body {
        font-family: var(--vscode-font-family);
        margin: 0;
        padding: 0;
        background-color: var(--background-color);
        color: var(--foreground-color);
        overflow-x: hidden;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      .container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
        word-wrap: break-word;
      }

      .screen {
        display: none;
        animation: fadeIn 0.3s ease-in;
      }

      .screen.active {
        display: block;
      }

      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }

      .welcome-screen {
        text-align: center;
        padding: 40px 20px;
      }

      .welcome-screen h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #007acc 0%, #00a0e4 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .version {
        color: var(--vscode-descriptionForeground);
        font-size: 1.1em;
        margin-bottom: 40px;
      }

      .mode-selection {
        display: flex;
        flex-direction: column;
        gap: 20px;
        align-items: center;
        padding: 20px;
      }

      .mode-card {
        width: 100%;
        max-width: 400px;
        padding: 20px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        word-wrap: break-word;
        overflow-wrap: break-word;
        box-sizing: border-box;
      }

      .mode-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .mode-card h3 {
        margin-top: 0;
        color: var(--primary-color);
      }

      .folder-info {
        background-color: var(--vscode-editor-inactiveSelectionBackground);
        padding: 15px;
        border-radius: 6px;
        margin: 20px 0;
        word-wrap: break-word;
        word-break: break-all;
        overflow-wrap: break-word;
        max-width: 100%;
      }

      .warning {
        background-color: var(--vscode-editorWarning-background);
        color: var(--vscode-editorWarning-foreground);
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
      }

      .error {
        background-color: var(--vscode-editorError-background);
        color: var(--vscode-editorError-foreground);
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
      }

      .time-config {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .time-input-group {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .advanced-options {
        margin-top: 20px;
      }

      .advanced-content {
        margin-top: 15px;
        padding: 15px;
        background-color: var(--vscode-editor-inactiveSelectionBackground);
        border-radius: 6px;
      }

      .file-tree {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 10px;
        margin-top: 10px;
      }

      .file-node {
        padding: 4px 0;
        display: flex;
        align-items: center;
        gap: 5px;
      }

      .file-node.directory {
        font-weight: bold;
      }

      .progress-section {
        margin-top: 30px;
      }

      .progress-bar {
        width: 100%;
        height: 20px;
        background-color: var(--vscode-progressBar-background);
        border-radius: 10px;
        overflow: hidden;
        margin: 10px 0;
        position: relative;
      }

      .progress-fill {
        height: 100%;
        background-color: var(--primary-color);
        transition: width 0.3s ease;
        position: relative;
      }

      .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
        font-weight: bold;
        color: var(--foreground-color);
        z-index: 1;
      }

      .stage-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background-color: var(--vscode-editor-inactiveSelectionBackground);
        border-radius: 6px;
        margin: 10px 0;
        border-left: 4px solid var(--primary-color);
      }

      .stage-details {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }

      .stage-title {
        font-weight: bold;
        font-size: 1.1em;
      }

      .stage-subtitle {
        color: var(--vscode-descriptionForeground);
        font-size: 0.9em;
      }

      .progress-timeline {
        margin-top: 20px;
        padding: 15px;
        background-color: var(--vscode-editor-inactiveSelectionBackground);
        border-radius: 6px;
      }

      .timeline-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 8px 0;
        border-bottom: 1px solid var(--border-color);
      }

      .timeline-item:last-child {
        border-bottom: none;
      }

      .timeline-icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
      }

      .timeline-icon.completed {
        background-color: #4caf50;
        color: white;
      }

      .timeline-icon.current {
        background-color: var(--primary-color);
        color: white;
        animation: pulse 1.5s infinite;
      }

      .timeline-icon.pending {
        background-color: var(--vscode-button-secondaryBackground);
        color: var(--vscode-button-secondaryForeground);
      }

      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }

      @keyframes slideInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes slideInDown {
        from {
          opacity: 0;
          transform: translateY(-30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .screen.active {
        display: block;
        animation: slideInUp 0.4s ease-out;
      }

      vscode-button {
        transition: all 0.2s ease;
        cursor: pointer;
      }

      vscode-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      vscode-button:active {
        transform: translateY(0);
      }

      .mode-card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
      }

      .mode-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
        transition: left 0.5s;
      }

      .mode-card:hover::before {
        left: 100%;
      }

      .file-node {
        transition: background-color 0.2s ease;
        border-radius: 4px;
        margin: 2px 0;
      }

      .file-node:hover {
        background-color: var(--vscode-list-hoverBackground);
      }

      .change-item {
        transition: all 0.3s ease;
      }

      .change-item:hover {
        background-color: var(--vscode-list-hoverBackground);
      }

      .progress-fill {
        background: linear-gradient(90deg, var(--primary-color), #00a0e4);
        position: relative;
        overflow: hidden;
      }

      .progress-fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-image: linear-gradient(
          -45deg,
          rgba(255, 255, 255, .2) 25%,
          transparent 25%,
          transparent 50%,
          rgba(255, 255, 255, .2) 50%,
          rgba(255, 255, 255, .2) 75%,
          transparent 75%,
          transparent
        );
        background-size: 50px 50px;
        animation: move 2s linear infinite;
      }

      @keyframes move {
        0% {
          background-position: 0 0;
        }
        100% {
          background-position: 50px 50px;
        }
      }

      /* Loading skeleton */
      .skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }

      @keyframes loading {
        0% {
          background-position: 200% 0;
        }
        100% {
          background-position: -200% 0;
        }
      }

      .skeleton-text {
        height: 16px;
        border-radius: 4px;
        margin: 8px 0;
      }

      .skeleton-text.short {
        width: 60%;
      }

      .skeleton-text.medium {
        width: 80%;
      }

      .skeleton-text.long {
        width: 100%;
      }

      /* Improved tooltips */
      .tooltip {
        position: relative;
        cursor: help;
      }

      .tooltip::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: var(--vscode-editorHoverWidget-background);
        color: var(--vscode-editorHoverWidget-foreground);
        border: 1px solid var(--vscode-editorHoverWidget-border);
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s;
        z-index: 1000;
      }

      .tooltip:hover::after {
        opacity: 1;
      }

      /* Enhanced focus styles */
      vscode-button:focus,
      vscode-text-field:focus,
      vscode-text-area:focus {
        outline: 2px solid var(--vscode-focusBorder);
        outline-offset: 2px;
      }

      /* Improved scrollbars */
      .changes-list::-webkit-scrollbar,
      .file-tree::-webkit-scrollbar {
        width: 8px;
      }

      .changes-list::-webkit-scrollbar-track,
      .file-tree::-webkit-scrollbar-track {
        background: var(--vscode-scrollbarSlider-background);
      }

      .changes-list::-webkit-scrollbar-thumb,
      .file-tree::-webkit-scrollbar-thumb {
        background: var(--vscode-scrollbarSlider-hoverBackground);
        border-radius: 4px;
      }

      .changes-list::-webkit-scrollbar-thumb:hover,
      .file-tree::-webkit-scrollbar-thumb:hover {
        background: var(--vscode-scrollbarSlider-activeBackground);
      }

      /* Path text styling */
      .path-text {
        font-family: var(--vscode-editor-font-family, 'Consolas', 'Courier New', monospace);
        font-size: 0.9em;
        word-break: break-all;
        overflow-wrap: break-word;
        white-space: pre-wrap;
        line-height: 1.4;
      }

      /* Ensure all text content wraps properly */
      p, div, span {
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      /* Specific styling for long paths */
      #currentFolderInfo, #sourcePath {
        word-break: break-all;
        overflow-wrap: break-word;
        white-space: pre-wrap;
        font-family: var(--vscode-editor-font-family, 'Consolas', 'Courier New', monospace);
        font-size: 0.9em;
        line-height: 1.4;
      }

      /* Responsive design for smaller screens */
      @media (max-width: 480px) {
        .container {
          padding: 10px;
        }

        .mode-card {
          padding: 15px;
          max-width: 100%;
        }

        .button-group {
          flex-direction: column;
          align-items: stretch;
        }

        .cancel-button {
          top: 5px;
          right: 5px;
        }

        .breadcrumb {
          font-size: 0.8em;
          padding: 5px 0;
        }

        .folder-info {
          padding: 10px;
          font-size: 0.9em;
        }
      }

      /* Status indicator */
      .status-indicator {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: var(--vscode-badge-background);
        color: var(--vscode-badge-foreground);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        z-index: 1000;
        animation: slideInUp 0.3s ease-out;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .status-indicator.success {
        background: #4caf50;
        color: white;
      }

      .status-indicator.error {
        background: #f44336;
        color: white;
      }

      .status-indicator.warning {
        background: #ff9800;
        color: white;
      }

      .changes-list {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        margin-top: 20px;
      }

      .change-item {
        padding: 10px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .change-item:last-child {
        border-bottom: none;
      }

      .change-item.active {
        background-color: var(--vscode-list-activeSelectionBackground);
        color: var(--vscode-list-activeSelectionForeground);
      }

      .change-item.completed {
        opacity: 0.6;
      }

      .button-group {
        display: flex;
        gap: 10px;
        margin-top: 20px;
        justify-content: center;
        flex-wrap: wrap;
        align-items: center;
      }

      .completion-screen {
        text-align: center;
        padding: 40px;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
        margin-top: 30px;
      }

      .stat-card {
        padding: 20px;
        background-color: var(--vscode-editor-inactiveSelectionBackground);
        border-radius: 8px;
        text-align: center;
      }

      .stat-value {
        font-size: 2em;
        font-weight: bold;
        color: var(--primary-color);
      }

      .stat-label {
        color: var(--vscode-descriptionForeground);
        margin-top: 5px;
      }

      vscode-button {
        margin: 0 5px;
      }

      vscode-text-field, vscode-text-area {
        width: 100%;
        margin: 10px 0;
      }

      .hidden {
        display: none !important;
      }

      .screen-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .cancel-button {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
      }

      .screen {
        position: relative;
      }

      .breadcrumb {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 20px;
        padding: 10px 0;
        border-bottom: 1px solid var(--border-color);
        font-size: 0.9em;
        color: var(--vscode-descriptionForeground);
        flex-wrap: wrap;
        overflow-x: hidden;
      }

      .breadcrumb-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .breadcrumb-item.active {
        color: var(--foreground-color);
        font-weight: bold;
      }

      .breadcrumb-separator {
        color: var(--vscode-descriptionForeground);
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- Welcome Screen -->
      <div id="welcomeScreen" class="screen active">
        <div class="welcome-screen">
          <h1>Wakatimer Pro</h1>
          <div class="version">Version 1.0.0</div>
          <vscode-button id="startButton">Start</vscode-button>
        </div>
      </div>

      <!-- Mode Selection Screen -->
      <div id="modeSelectionScreen" class="screen">
        <vscode-button id="cancelModeSelection" class="cancel-button" appearance="secondary">✕</vscode-button>
        <div class="breadcrumb">
          <div class="breadcrumb-item">Welcome</div>
          <div class="breadcrumb-separator">›</div>
          <div class="breadcrumb-item active">Mode Selection</div>
        </div>
        <h2>Choose Your Mode</h2>
        <div class="mode-selection">
          <div class="mode-card tooltip" id="editInPlaceCard" data-tooltip="Modify files directly in your current workspace">
            <h3>Edit in Place</h3>
            <p>Make changes directly to your current workspace folder</p>
          </div>
          <div class="mode-card tooltip" id="makeCopyCard" data-tooltip="Copy files from another location before modifying">
            <h3>Make a Copy and Edit</h3>
            <p>Copy files from a source folder to your workspace and edit the copies</p>
          </div>
        </div>
      </div>

      <!-- Folder Selection Screen -->
      <div id="folderSelectionScreen" class="screen">
        <vscode-button id="cancelFolderSelection" class="cancel-button" appearance="secondary">✕</vscode-button>
        <div class="breadcrumb">
          <div class="breadcrumb-item">Welcome</div>
          <div class="breadcrumb-separator">›</div>
          <div class="breadcrumb-item">Mode Selection</div>
          <div class="breadcrumb-separator">›</div>
          <div class="breadcrumb-item active">Folder Selection</div>
        </div>
        <h2 id="folderSelectionTitle">Select Working Folder</h2>
        <div class="folder-info">
          <p id="currentFolderInfo">No folder currently open</p>
        </div>
        <div id="folderWarning" class="warning hidden"></div>
        <vscode-button id="openFolderButton">Open Folder</vscode-button>
        <vscode-button id="folderNextButton" disabled>Next</vscode-button>
      </div>

      <!-- Source Selection Screen (for copy mode) -->
      <div id="sourceSelectionScreen" class="screen">
        <vscode-button id="cancelSourceSelection" class="cancel-button" appearance="secondary">✕</vscode-button>
        <h2>Select Source Folder</h2>
        <vscode-button id="selectSourceButton">Select Source Folder</vscode-button>
        <div id="sourcePreview" class="folder-info hidden">
          <p id="sourcePath"></p>
        </div>
        <div class="advanced-options">
          <vscode-link id="advancedSourceOptions">Advanced Options</vscode-link>
          <div id="advancedSourceContent" class="advanced-content hidden">
            <p>Select files and folders to copy:</p>
            <div id="fileTree" class="file-tree"></div>
          </div>
        </div>
        <vscode-button id="sourceNextButton" disabled>Next</vscode-button>
      </div>

      <!-- Time Configuration Screen -->
      <div id="timeConfigScreen" class="screen">
        <vscode-button id="cancelTimeConfig" class="cancel-button" appearance="secondary">✕</vscode-button>
        <div class="breadcrumb">
          <div class="breadcrumb-item">Welcome</div>
          <div class="breadcrumb-separator">›</div>
          <div class="breadcrumb-item">Mode Selection</div>
          <div class="breadcrumb-separator">›</div>
          <div class="breadcrumb-item">Folder Setup</div>
          <div class="breadcrumb-separator">›</div>
          <div class="breadcrumb-item active">Time Configuration</div>
        </div>
        <h2>Configure Time Period</h2>
        <div class="time-config">
          <div class="time-input-group">
            <label for="daysInput">Number of days:</label>
            <vscode-text-field id="daysInput" type="number" value="1" min="1"></vscode-text-field>
          </div>
          <div class="time-input-group">
            <label for="hoursInput">Hours per day:</label>
            <vscode-text-field id="hoursInput" type="number" value="8" min="1" max="24"></vscode-text-field>
          </div>
        </div>
        <div class="advanced-options">
          <vscode-link id="advancedTimeOptions">Advanced Options</vscode-link>
          <div id="advancedTimeContent" class="advanced-content hidden">
            <p>Customize hours for each day:</p>
            <div id="dailyHours"></div>
          </div>
        </div>
        <vscode-button id="timeNextButton">Next</vscode-button>
      </div>

      <!-- Mode Type Selection Screen -->
      <div id="modeTypeScreen" class="screen">
        <vscode-button id="cancelModeType" class="cancel-button" appearance="secondary">✕</vscode-button>
        <h2>Choose Processing Mode</h2>
        <div class="mode-selection">
          <div class="mode-card tooltip" id="simpleModeCard" data-tooltip="Fully automated using GitHub Copilot or similar AI">
            <h3>Simple Mode</h3>
            <p>Automatically generate changes using VS Code's built-in AI</p>
          </div>
          <div class="mode-card tooltip" id="advancedModeCard" data-tooltip="Use external AI tools like ChatGPT or Claude">
            <h3>Advanced Mode</h3>
            <p>Generate a prompt to use with external AI tools</p>
          </div>
        </div>
      </div>

      <!-- AI Processing Screen -->
      <div id="aiProcessingScreen" class="screen">
        <vscode-button id="cancelAIProcessing" class="cancel-button" appearance="secondary">✕</vscode-button>
        <h2>Processing</h2>
        <div class="progress-section">
          <div class="stage-info">
            <div class="stage-details">
              <div class="stage-title" id="currentStage">Initializing...</div>
              <div class="stage-subtitle" id="stageSubtitle">Preparing to analyze your project</div>
            </div>
            <vscode-progress-ring></vscode-progress-ring>
          </div>
          <div class="progress-bar">
            <div id="progressFill" class="progress-fill" style="width: 0%">
              <div class="progress-text" id="progressText">0%</div>
            </div>
          </div>
          <div class="progress-timeline">
            <div class="timeline-item">
              <div class="timeline-icon pending" id="analyzeIcon">1</div>
              <div>
                <div>Analyzing project structure</div>
                <small>Scanning files and directories</small>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-icon pending" id="generateIcon">2</div>
              <div>
                <div>Generating changes with AI</div>
                <small>Creating meaningful code modifications</small>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-icon pending" id="verifyIcon">3</div>
              <div>
                <div>Verifying changes</div>
                <small>Ensuring changes are valid and safe</small>
              </div>
            </div>
          </div>
        </div>
        <div id="errorSection" class="error hidden">
          <p id="errorMessage"></p>
          <div class="button-group">
            <vscode-button id="retryButton">Retry</vscode-button>
            <vscode-button id="continueButton" appearance="secondary">Continue Anyway</vscode-button>
          </div>
        </div>
      </div>

      <!-- Advanced Mode Prompt Screen -->
      <div id="promptScreen" class="screen">
        <h2>Generated Prompt</h2>
        <p>Copy this prompt and use it with your preferred AI tool:</p>
        <vscode-text-area id="promptTextArea" rows="10" readonly></vscode-text-area>
        <vscode-button id="copyPromptButton">Copy to Clipboard</vscode-button>
        <hr style="margin: 30px 0;">
        <p>Paste the AI response here:</p>
        <vscode-text-area id="responseTextArea" rows="10" placeholder="Paste AI response here..."></vscode-text-area>
        <vscode-button id="processResponseButton">Process Response</vscode-button>
      </div>

      <!-- Changes Preview Screen -->
      <div id="changesPreviewScreen" class="screen">
        <h2>Changes Ready</h2>
        <div class="stage-info">
          <span>Total changes: <strong id="totalChangesCount">0</strong></span>
          <vscode-button id="previewChangesButton" appearance="secondary">Preview Changes</vscode-button>
        </div>
        <div class="changes-list" id="changesList"></div>
        <div class="button-group">
          <vscode-button id="startProcedureButton" appearance="primary">Start Procedure</vscode-button>
        </div>
      </div>

      <!-- Change Application Screen -->
      <div id="applicationScreen" class="screen">
        <h2>Applying Changes</h2>
        <div class="progress-section">
          <div class="stage-info">
            <span>Progress: <strong id="currentChangeIndex">0</strong> / <strong id="totalChanges">0</strong></span>
            <span id="timeRemaining"></span>
          </div>
          <div class="progress-bar">
            <div id="changeProgressFill" class="progress-fill" style="width: 0%"></div>
          </div>
          <p id="currentChangeInfo"></p>
        </div>
        <div class="changes-list" id="changesTimeline"></div>
        <div class="button-group">
          <vscode-button id="pauseButton">Pause</vscode-button>
          <vscode-button id="resumeButton" class="hidden">Resume</vscode-button>
        </div>
      </div>

      <!-- Completion Screen -->
      <div id="completionScreen" class="screen">
        <div class="completion-screen">
          <h1>✅ Procedure Complete!</h1>
          <p>All changes have been successfully applied.</p>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-value" id="totalChangesApplied">0</div>
              <div class="stat-label">Changes Applied</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="totalDuration">0h 0m</div>
              <div class="stat-label">Total Duration</div>
            </div>
            <div class="stat-card">
              <div class="stat-value" id="filesModified">0</div>
              <div class="stat-label">Files Modified</div>
            </div>
          </div>
          <div class="button-group">
            <vscode-button id="newSessionButton">Start New Session</vscode-button>
          </div>
        </div>
      </div>
    </div>

    <script>
      const vscode = acquireVsCodeApi();
      let state = {
        currentScreen: 'welcome',
        mode: null,
        workingDirectory: null,
        sourcePath: null,
        selectedFiles: [],
        days: 1,
        hoursPerDay: 8,
        dailyHours: {},
        processingMode: null,
        changes: [],
        currentChangeIndex: 0
      };

      // Screen management
      function showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
          screen.classList.remove('active');
        });
        document.getElementById(screenId + 'Screen').classList.add('active');
        state.currentScreen = screenId;
      }

      // Error handling
      function showError(title, message, canRetry = false) {
        const errorOverlay = document.createElement('div');
        errorOverlay.className = 'error-overlay';
        errorOverlay.style.position = 'fixed';
        errorOverlay.style.top = '0';
        errorOverlay.style.left = '0';
        errorOverlay.style.right = '0';
        errorOverlay.style.bottom = '0';
        errorOverlay.style.background = 'rgba(0,0,0,0.5)';
        errorOverlay.style.zIndex = '10000';
        errorOverlay.style.display = 'flex';
        errorOverlay.style.alignItems = 'center';
        errorOverlay.style.justifyContent = 'center';

        const errorDialog = document.createElement('div');
        errorDialog.style.background = 'var(--background-color)';
        errorDialog.style.border = '1px solid var(--border-color)';
        errorDialog.style.borderRadius = '8px';
        errorDialog.style.padding = '20px';
        errorDialog.style.maxWidth = '400px';
        errorDialog.style.width = '90%';

        const titleEl = document.createElement('h3');
        titleEl.style.marginTop = '0';
        titleEl.style.color = 'var(--vscode-errorForeground)';
        titleEl.textContent = title;

        const messageEl = document.createElement('p');
        messageEl.textContent = message;

        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'button-group';

        const okButton = document.createElement('vscode-button');
        okButton.setAttribute('appearance', 'primary');
        okButton.textContent = 'OK';
        okButton.addEventListener('click', () => {
          errorOverlay.remove();
        });

        buttonGroup.appendChild(okButton);

        if (canRetry) {
          const retryButton = document.createElement('vscode-button');
          retryButton.textContent = 'Retry';
          retryButton.addEventListener('click', () => {
            errorOverlay.remove();
            // Retry logic would go here based on current state
          });
          buttonGroup.appendChild(retryButton);
        }

        errorDialog.appendChild(titleEl);
        errorDialog.appendChild(messageEl);
        errorDialog.appendChild(buttonGroup);
        errorOverlay.appendChild(errorDialog);
        document.body.appendChild(errorOverlay);
      }

      // Copy progress dialog
      function showCopyProgress(stage, message, progress) {
        let progressOverlay = document.getElementById('copyProgressOverlay');

        if (!progressOverlay) {
          progressOverlay = document.createElement('div');
          progressOverlay.id = 'copyProgressOverlay';
          progressOverlay.style.position = 'fixed';
          progressOverlay.style.top = '0';
          progressOverlay.style.left = '0';
          progressOverlay.style.right = '0';
          progressOverlay.style.bottom = '0';
          progressOverlay.style.background = 'rgba(0,0,0,0.5)';
          progressOverlay.style.zIndex = '9999';
          progressOverlay.style.display = 'flex';
          progressOverlay.style.alignItems = 'center';
          progressOverlay.style.justifyContent = 'center';

          const progressDialog = document.createElement('div');
          progressDialog.style.background = 'var(--background-color)';
          progressDialog.style.border = '1px solid var(--border-color)';
          progressDialog.style.borderRadius = '8px';
          progressDialog.style.padding = '20px';
          progressDialog.style.maxWidth = '400px';
          progressDialog.style.width = '90%';

          const titleEl = document.createElement('h3');
          titleEl.style.marginTop = '0';
          titleEl.textContent = 'Copying Files';

          const messageEl = document.createElement('p');
          messageEl.id = 'copyProgressMessage';

          const progressBar = document.createElement('div');
          progressBar.className = 'progress-bar';
          progressBar.style.marginTop = '15px';

          const progressFill = document.createElement('div');
          progressFill.id = 'copyProgressFill';
          progressFill.className = 'progress-fill';
          progressFill.style.width = '0%';

          const progressText = document.createElement('div');
          progressText.id = 'copyProgressText';
          progressText.className = 'progress-text';
          progressText.textContent = '0%';

          progressBar.appendChild(progressFill);
          progressBar.appendChild(progressText);
          progressDialog.appendChild(titleEl);
          progressDialog.appendChild(messageEl);
          progressDialog.appendChild(progressBar);
          progressOverlay.appendChild(progressDialog);
          document.body.appendChild(progressOverlay);
        }

        document.getElementById('copyProgressMessage').textContent = message;
        document.getElementById('copyProgressFill').style.width = progress + '%';
        document.getElementById('copyProgressText').textContent = progress + '%';
      }

      function hideCopyProgress() {
        const progressOverlay = document.getElementById('copyProgressOverlay');
        if (progressOverlay) {
          progressOverlay.remove();
        }
      }

      // Confirmation dialog
      function showConfirmDialog(title, message, onConfirm, onCancel = null) {
        const confirmOverlay = document.createElement('div');
        confirmOverlay.className = 'confirm-overlay';
        confirmOverlay.style.position = 'fixed';
        confirmOverlay.style.top = '0';
        confirmOverlay.style.left = '0';
        confirmOverlay.style.right = '0';
        confirmOverlay.style.bottom = '0';
        confirmOverlay.style.background = 'rgba(0,0,0,0.5)';
        confirmOverlay.style.zIndex = '10001';
        confirmOverlay.style.display = 'flex';
        confirmOverlay.style.alignItems = 'center';
        confirmOverlay.style.justifyContent = 'center';

        const confirmDialog = document.createElement('div');
        confirmDialog.style.background = 'var(--background-color)';
        confirmDialog.style.border = '1px solid var(--border-color)';
        confirmDialog.style.borderRadius = '8px';
        confirmDialog.style.padding = '20px';
        confirmDialog.style.maxWidth = '400px';
        confirmDialog.style.width = '90%';
        confirmDialog.style.animation = 'slideInDown 0.3s ease-out';

        const titleEl = document.createElement('h3');
        titleEl.style.marginTop = '0';
        titleEl.style.color = 'var(--vscode-editorWarning-foreground)';
        titleEl.textContent = title;

        const messageEl = document.createElement('p');
        messageEl.textContent = message;

        const buttonGroup = document.createElement('div');
        buttonGroup.className = 'button-group';
        buttonGroup.style.justifyContent = 'flex-end';

        const cancelButton = document.createElement('vscode-button');
        cancelButton.setAttribute('appearance', 'secondary');
        cancelButton.textContent = 'Cancel';
        cancelButton.addEventListener('click', () => {
          confirmOverlay.remove();
          if (onCancel) onCancel();
        });

        const confirmButton = document.createElement('vscode-button');
        confirmButton.setAttribute('appearance', 'primary');
        confirmButton.textContent = 'Confirm';
        confirmButton.addEventListener('click', () => {
          confirmOverlay.remove();
          onConfirm();
        });

        buttonGroup.appendChild(cancelButton);
        buttonGroup.appendChild(confirmButton);
        confirmDialog.appendChild(titleEl);
        confirmDialog.appendChild(messageEl);
        confirmDialog.appendChild(buttonGroup);
        confirmOverlay.appendChild(confirmDialog);
        document.body.appendChild(confirmOverlay);

        // Focus the confirm button
        setTimeout(() => confirmButton.focus(), 100);
      }

      // Status notifications
      function showStatus(message, type = 'info', duration = 3000) {
        // Remove existing status indicator
        const existing = document.querySelector('.status-indicator');
        if (existing) {
          existing.remove();
        }

        const statusIndicator = document.createElement('div');
        statusIndicator.className = 'status-indicator ' + type;
        statusIndicator.textContent = message;
        document.body.appendChild(statusIndicator);

        // Auto-remove after duration
        setTimeout(() => {
          if (statusIndicator.parentNode) {
            statusIndicator.style.animation = 'slideInDown 0.3s ease-out reverse';
            setTimeout(() => statusIndicator.remove(), 300);
          }
        }, duration);
      }

      // Event handlers
      document.getElementById('startButton').addEventListener('click', () => {
        showScreen('modeSelection');
      });

      document.getElementById('editInPlaceCard').addEventListener('click', () => {
        state.mode = 'editInPlace';
        vscode.postMessage({ type: 'checkFolderEmpty' });
        showScreen('folderSelection');
        document.getElementById('folderSelectionTitle').textContent = 'Select Working Folder';
      });

      document.getElementById('makeCopyCard').addEventListener('click', () => {
        state.mode = 'makeCopy';
        vscode.postMessage({ type: 'checkFolderEmpty' });
        showScreen('folderSelection');
        document.getElementById('folderSelectionTitle').textContent = 'Select Destination Folder';
      });

      document.getElementById('openFolderButton').addEventListener('click', () => {
        vscode.postMessage({ type: 'openFolder' });
      });

      document.getElementById('folderNextButton').addEventListener('click', () => {
        if (state.mode === 'makeCopy') {
          showScreen('sourceSelection');
        } else {
          showScreen('timeConfig');
        }
      });

      document.getElementById('selectSourceButton').addEventListener('click', () => {
        vscode.postMessage({ type: 'selectSourceFolder' });
      });

      document.getElementById('sourceNextButton').addEventListener('click', () => {
        vscode.postMessage({
          type: 'copyFiles',
          data: {
            sourcePath: state.sourcePath,
            destinationPath: state.workingDirectory,
            selectedFiles: state.selectedFiles
          }
        });
      });

      document.getElementById('timeNextButton').addEventListener('click', () => {
        state.days = parseInt(document.getElementById('daysInput').value);
        state.hoursPerDay = parseInt(document.getElementById('hoursInput').value);
        showScreen('modeType');
      });

      document.getElementById('simpleModeCard').addEventListener('click', () => {
        state.processingMode = 'simple';
        showScreen('aiProcessing');
        vscode.postMessage({
          type: 'startSimpleMode',
          data: {
            workingDirectory: state.workingDirectory,
            days: state.days,
            hoursPerDay: state.hoursPerDay,
            dailyHours: state.dailyHours
          }
        });
      });

      document.getElementById('advancedModeCard').addEventListener('click', () => {
        state.processingMode = 'advanced';
        showScreen('prompt');
        vscode.postMessage({
          type: 'generatePrompt',
          data: {
            workingDirectory: state.workingDirectory,
            days: state.days,
            hoursPerDay: state.hoursPerDay,
            dailyHours: state.dailyHours
          }
        });
      });

      document.getElementById('copyPromptButton').addEventListener('click', () => {
        const promptText = document.getElementById('promptTextArea').value;
        navigator.clipboard.writeText(promptText);
        // Show feedback
        const button = document.getElementById('copyPromptButton');
        button.textContent = 'Copied!';
        setTimeout(() => {
          button.textContent = 'Copy to Clipboard';
        }, 2000);
      });

      document.getElementById('processResponseButton').addEventListener('click', () => {
        const response = document.getElementById('responseTextArea').value;
        if (response.trim()) {
          showScreen('aiProcessing');
          vscode.postMessage({
            type: 'processAIResponse',
            data: {
              response,
              config: {
                workingDirectory: state.workingDirectory,
                days: state.days,
                hoursPerDay: state.hoursPerDay,
                dailyHours: state.dailyHours
              }
            }
          });
        }
      });

      document.getElementById('previewChangesButton').addEventListener('click', () => {
        vscode.postMessage({ type: 'previewChanges' });
      });

      document.getElementById('startProcedureButton').addEventListener('click', () => {
        showConfirmDialog(
          'Start Procedure',
          'This will begin applying changes to your files. This action cannot be undone. Are you sure you want to continue?',
          () => {
            showScreen('application');
            vscode.postMessage({ type: 'startProcedure' });
          }
        );
      });

      document.getElementById('pauseButton').addEventListener('click', () => {
        vscode.postMessage({ type: 'pauseProcedure' });
        document.getElementById('pauseButton').classList.add('hidden');
        document.getElementById('resumeButton').classList.remove('hidden');
      });

      document.getElementById('resumeButton').addEventListener('click', () => {
        vscode.postMessage({ type: 'resumeProcedure' });
        document.getElementById('resumeButton').classList.add('hidden');
        document.getElementById('pauseButton').classList.remove('hidden');
      });

      document.getElementById('newSessionButton').addEventListener('click', () => {
        // Reset state
        state = {
          currentScreen: 'welcome',
          mode: null,
          workingDirectory: null,
          sourcePath: null,
          selectedFiles: [],
          days: 1,
          hoursPerDay: 8,
          dailyHours: {},
          processingMode: null,
          changes: [],
          currentChangeIndex: 0
        };
        showScreen('welcome');
      });

      // Cancel button handlers
      document.getElementById('cancelModeSelection').addEventListener('click', () => {
        showScreen('welcome');
      });

      document.getElementById('cancelFolderSelection').addEventListener('click', () => {
        showScreen('modeSelection');
      });

      document.getElementById('cancelSourceSelection').addEventListener('click', () => {
        showScreen('folderSelection');
      });

      document.getElementById('cancelTimeConfig').addEventListener('click', () => {
        if (state.mode === 'makeCopy') {
          showScreen('sourceSelection');
        } else {
          showScreen('folderSelection');
        }
      });

      document.getElementById('cancelModeType').addEventListener('click', () => {
        showScreen('timeConfig');
      });

      document.getElementById('cancelAIProcessing').addEventListener('click', () => {
        if (confirm('Are you sure you want to cancel the AI processing? This will stop the current operation.')) {
          showScreen('modeType');
        }
      });

      // Advanced options toggles
      document.getElementById('advancedSourceOptions').addEventListener('click', (e) => {
        e.preventDefault();
        const content = document.getElementById('advancedSourceContent');
        content.classList.toggle('hidden');
      });

      document.getElementById('advancedTimeOptions').addEventListener('click', (e) => {
        e.preventDefault();
        const content = document.getElementById('advancedTimeContent');
        content.classList.toggle('hidden');
        if (!content.classList.contains('hidden')) {
          updateDailyHoursInputs();
        }
      });

      // Time configuration
      document.getElementById('daysInput').addEventListener('change', updateDailyHoursInputs);

      function updateDailyHoursInputs() {
        const days = parseInt(document.getElementById('daysInput').value);
        const container = document.getElementById('dailyHours');
        container.innerHTML = '';
        
        if (days > 1) {
          for (let i = 1; i <= days; i++) {
            const div = document.createElement('div');
            div.className = 'time-input-group';
            div.innerHTML =
              '<label for="day' + i + 'Hours">Day ' + i + ':</label>' +
              '<vscode-text-field id="day' + i + 'Hours" type="number" value="' + state.hoursPerDay + '" min="1" max="24"></vscode-text-field>';
            container.appendChild(div);
          }
        }
      }

      // Message handling
      window.addEventListener('message', event => {
        const message = event.data;
        switch (message.type) {
          case 'folderEmptyStatus':
            if (state.mode === 'makeCopy' && !message.isEmpty) {
              document.getElementById('folderWarning').textContent = 'Warning: The destination folder is not empty.';
              document.getElementById('folderWarning').classList.remove('hidden');
              document.getElementById('folderNextButton').disabled = true;
            } else {
              document.getElementById('folderWarning').classList.add('hidden');
              document.getElementById('folderNextButton').disabled = false;
              // Workspace folder info will be sent from the extension
              if (message.workspaceFolder) {
                state.workingDirectory = message.workspaceFolder;
                document.getElementById('currentFolderInfo').textContent = 'Current folder: ' + state.workingDirectory;
              }
            }
            break;

          case 'sourceStructure':
            state.sourcePath = message.data.path;
            document.getElementById('sourcePath').textContent = 'Source: ' + message.data.path;
            document.getElementById('sourcePreview').classList.remove('hidden');
            renderFileTree(message.data.structure);
            document.getElementById('sourceNextButton').disabled = false;
            break;

          case 'copyProgress':
            showCopyProgress(message.stage, message.message, message.progress);
            break;

          case 'copyComplete':
            hideCopyProgress();
            if (message.success) {
              showStatus('Files copied successfully!', 'success');
              showScreen('timeConfig');
            } else {
              showStatus('Failed to copy files', 'error');
              showError('Copy Failed', 'Failed to copy files: ' + message.error, true);
            }
            break;

          case 'aiProgress':
            updateAIProgress(message.stage, message.message);
            break;

          case 'aiError':
            document.getElementById('errorMessage').textContent = message.error;
            document.getElementById('errorSection').classList.remove('hidden');
            if (!message.canRetry) {
              document.getElementById('continueButton').classList.add('hidden');
            }
            break;

          case 'changesReady':
            state.changes = message.data.changes;
            document.getElementById('totalChangesCount').textContent = message.data.totalChanges;
            renderChangesList(message.data.changes);
            showStatus(message.data.totalChanges + ' changes ready for application', 'success');
            showScreen('changesPreview');
            break;

          case 'promptGenerated':
            document.getElementById('promptTextArea').value = message.data;
            break;

          case 'changeProgress':
            state.currentChangeIndex = message.data.current;
            updateChangeProgress(message.data);
            break;

          case 'procedureComplete':
            document.getElementById('totalChangesApplied').textContent = message.data.totalChanges;
            document.getElementById('totalDuration').textContent = message.data.duration;
            document.getElementById('filesModified').textContent = new Set(state.changes.map(c => c.file)).size;
            showScreen('completion');
            break;

          case 'generalError':
            showError('Operation Failed', message.error, true);
            break;
        }
      });

      function renderFileTree(node) {
        const container = document.getElementById('fileTree');
        container.innerHTML = '';
        container.treeData = node; // Store tree data for later access
        renderFileNode(node, container, 0);
        updateSelectedFiles();
      }

      function renderFileNode(node, container, level) {
        const div = document.createElement('div');
        div.className = 'file-node ' + node.type;
        div.style.paddingLeft = (level * 20) + 'px';
        
        const checkbox = document.createElement('vscode-checkbox');
        checkbox.checked = node.selected !== false;
        checkbox.addEventListener('change', (e) => {
          node.selected = e.target.checked;
          updateSelectedFiles();
        });
        
        const label = document.createElement('span');
        label.textContent = node.name;
        
        div.appendChild(checkbox);
        div.appendChild(label);
        container.appendChild(div);
        
        if (node.children) {
          node.children.forEach(child => {
            renderFileNode(child, container, level + 1);
          });
        }
      }

      function updateSelectedFiles() {
        state.selectedFiles = [];
        function collectSelected(node) {
          if (node.selected && node.type === 'file') {
            state.selectedFiles.push(node.path);
          }
          if (node.children) {
            node.children.forEach(collectSelected);
          }
        }
        // Collect from tree if available
        const treeData = document.getElementById('fileTree').treeData;
        if (treeData) {
          collectSelected(treeData);
        }
        
        document.getElementById('sourceNextButton').disabled = state.selectedFiles.length === 0;
      }

      function renderChangesList(changes) {
        const container = document.getElementById('changesList');
        container.innerHTML = '';
        changes.forEach((change, index) => {
          const div = document.createElement('div');
          div.className = 'change-item';
          div.innerHTML =
            '<div>' +
            '<strong>' + change.file + '</strong><br>' +
            '<small>' + change.description + ' (' + change.lineCount + ' lines)</small>' +
            '</div>';
          container.appendChild(div);
        });
      }

      function updateAIProgress(stage, message) {
        document.getElementById('currentStage').textContent = message;

        // Update progress bar and timeline based on stage
        let progress = 0;
        let subtitle = '';

        // Reset all icons to pending
        document.getElementById('analyzeIcon').className = 'timeline-icon pending';
        document.getElementById('generateIcon').className = 'timeline-icon pending';
        document.getElementById('verifyIcon').className = 'timeline-icon pending';

        switch (stage) {
          case 'analyzing':
            progress = 33;
            subtitle = 'Scanning your project files and structure';
            document.getElementById('analyzeIcon').className = 'timeline-icon current';
            break;
          case 'generating':
            progress = 66;
            subtitle = 'AI is creating meaningful code changes';
            document.getElementById('analyzeIcon').className = 'timeline-icon completed';
            document.getElementById('analyzeIcon').textContent = '✓';
            document.getElementById('generateIcon').className = 'timeline-icon current';
            break;
          case 'verifying':
            progress = 90;
            subtitle = 'Validating changes for safety and correctness';
            document.getElementById('analyzeIcon').className = 'timeline-icon completed';
            document.getElementById('analyzeIcon').textContent = '✓';
            document.getElementById('generateIcon').className = 'timeline-icon completed';
            document.getElementById('generateIcon').textContent = '✓';
            document.getElementById('verifyIcon').className = 'timeline-icon current';
            break;
          case 'complete':
            progress = 100;
            subtitle = 'All changes have been prepared successfully';
            document.getElementById('analyzeIcon').className = 'timeline-icon completed';
            document.getElementById('analyzeIcon').textContent = '✓';
            document.getElementById('generateIcon').className = 'timeline-icon completed';
            document.getElementById('generateIcon').textContent = '✓';
            document.getElementById('verifyIcon').className = 'timeline-icon completed';
            document.getElementById('verifyIcon').textContent = '✓';
            break;
        }

        document.getElementById('stageSubtitle').textContent = subtitle;
        document.getElementById('progressFill').style.width = progress + '%';
        document.getElementById('progressText').textContent = Math.round(progress) + '%';
      }

      function updateChangeProgress(data) {
        document.getElementById('currentChangeIndex').textContent = data.current;
        document.getElementById('totalChanges').textContent = data.total;
        document.getElementById('currentChangeInfo').textContent =
          'Applying change to ' + data.currentFile + ': ' + data.description;

        const progress = (data.current / data.total) * 100;
        document.getElementById('changeProgressFill').style.width = progress + '%';

        // Update timeline
        const timeline = document.getElementById('changesTimeline');
        timeline.innerHTML = '';
        state.changes.forEach((change, index) => {
          const div = document.createElement('div');
          div.className = 'change-item';
          if (index < data.current - 1) {
            div.classList.add('completed');
          } else if (index === data.current - 1) {
            div.classList.add('active');
          }
          div.innerHTML =
            '<div>' +
            '<strong>' + change.file + '</strong><br>' +
            '<small>' + change.description + '</small>' +
            '</div>' +
            '<div>' +
            (index < data.current - 1 ? '✓' : index === data.current - 1 ? '⏳' : '⏸') +
            '</div>';
          timeline.appendChild(div);
        });

        // Calculate time remaining
        const remainingChanges = data.total - data.current;
        const remainingSeconds = remainingChanges * 90;
        const hours = Math.floor(remainingSeconds / 3600);
        const minutes = Math.floor((remainingSeconds % 3600) / 60);
        document.getElementById('timeRemaining').textContent =
          'Time remaining: ' + hours + 'h ' + minutes + 'm';
      }

      // Keyboard shortcuts
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          // Handle escape key based on current screen
          switch (state.currentScreen) {
            case 'modeSelection':
              showScreen('welcome');
              break;
            case 'folderSelection':
              showScreen('modeSelection');
              break;
            case 'sourceSelection':
              showScreen('folderSelection');
              break;
            case 'timeConfig':
              if (state.mode === 'makeCopy') {
                showScreen('sourceSelection');
              } else {
                showScreen('folderSelection');
              }
              break;
            case 'modeType':
              showScreen('timeConfig');
              break;
            case 'aiProcessing':
              if (confirm('Are you sure you want to cancel the AI processing?')) {
                showScreen('modeType');
              }
              break;
          }
        }
      });

      // Initialize - workspace folder will be sent from the extension
    </script>
  </body>
  </html>`;
}
