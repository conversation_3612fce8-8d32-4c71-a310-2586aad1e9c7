import * as vscode from 'vscode';
import { Change, LineChange } from '../types';
import * as path from 'path';

export class ChangeProcessor {
    async verifyChanges(changes: Change[]): Promise<Change[]> {
        const verifiedChanges: Change[] = [];
        
        for (const change of changes) {
            try {
                // Check if file exists
                const uri = vscode.Uri.file(change.file);
                const stat = await vscode.workspace.fs.stat(uri);
                
                if (stat.type === vscode.FileType.File) {
                    // Read file content to verify line numbers
                    const content = await vscode.workspace.fs.readFile(uri);
                    const lines = Buffer.from(content).toString('utf8').split('\n');
                    
                    // Verify each line change
                    const validLineChanges: LineChange[] = [];
                    for (const lineChange of change.changes) {
                        if (lineChange.lineNumber > 0 && lineChange.lineNumber <= lines.length) {
                            validLineChanges.push(lineChange);
                        }
                    }
                    
                    if (validLineChanges.length > 0) {
                        verifiedChanges.push({
                            ...change,
                            changes: validLineChanges
                        });
                    }
                }
            } catch (error) {
                console.error(`Error verifying change for file ${change.file}:`, error);
            }
        }
        
        return verifiedChanges;
    }

    async applyChange(change: Change): Promise<void> {
        const uri = vscode.Uri.file(change.file);
        const document = await vscode.workspace.openTextDocument(uri);
        const editor = await vscode.window.showTextDocument(document, { preview: false });

        // Sort changes by line number in descending order to avoid offset issues
        const sortedChanges = [...change.changes].sort((a, b) => b.lineNumber - a.lineNumber);

        await editor.edit(editBuilder => {
            for (const lineChange of sortedChanges) {
                const lineIndex = lineChange.lineNumber - 1;
                if (lineIndex >= 0 && lineIndex < document.lineCount) {
                    const line = document.lineAt(lineIndex);
                    const range = new vscode.Range(
                        line.range.start,
                        line.range.end
                    );
                    editBuilder.replace(range, lineChange.newContent);
                }
            }
        });
        
        // Save the document
        await document.save();
    }

    generatePreview(changes: Change[]): string {
        let content = 'WAKATIMER PRO - CHANGES PREVIEW\n';
        content += '================================\n\n';
        
        for (const change of changes) {
            const fileName = path.basename(change.file);
            content += `--- ${fileName} ---\n`;
            content += `Description: ${change.description}\n`;
            content += `Full path: ${change.file}\n\n`;
            
            for (const lineChange of change.changes) {
                content += `@@ Line ${lineChange.lineNumber} @@\n`;
                content += `- ${lineChange.oldContent}\n`;
                content += `+ ${lineChange.newContent}\n\n`;
            }
            content += '\n';
        }
        
        content += `\nTotal changes: ${changes.length}\n`;
        content += `Files affected: ${new Set(changes.map(c => c.file)).size}\n`;
        
        return content;
    }
}
