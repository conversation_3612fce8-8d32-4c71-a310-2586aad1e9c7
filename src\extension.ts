import * as vscode from 'vscode';
import { WakatimerProViewProvider } from './webviewProvider';

export function activate(context: vscode.ExtensionContext) {
    console.log('Wakatimer Pro is now active!');

    // Create and register the webview provider
    const provider = new WakatimerProViewProvider(context.extensionUri, context);
    
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(
            WakatimerProViewProvider.viewType,
            provider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        )
    );

    // Register the command to show the panel
    context.subscriptions.push(
        vscode.commands.registerCommand('wakatimer-pro.showPanel', () => {
            provider.show();
        })
    );
}

export function deactivate() {
    console.log('Wakatimer Pro is now deactivated');
}
