{"name": "wakatimer-pro", "displayName": "Wakatimer Pro", "description": "Generate retroactive time tracking data with AI assistance", "version": "1.0.0", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "engines": {"vscode": "^1.96.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "wakatimer-pro.showPanel", "title": "Show Wakatimer Pro"}], "viewsContainers": {"activitybar": [{"id": "wakatimer-pro", "title": "Wakatimer Pro", "icon": "media/icon.svg"}]}, "views": {"wakatimer-pro": [{"type": "webview", "id": "wakatimer-pro.main<PERSON>iew", "name": "Wakatimer Pro", "icon": "media/icon.svg", "contextualTitle": "Wakatimer Pro"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "vscode-test"}, "devDependencies": {"@types/node": "20.x", "@types/vscode": "^1.101.0", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.4.1", "eslint": "^9.16.0", "typescript": "^5.7.2"}, "dependencies": {"@vscode/webview-ui-toolkit": "^1.4.0"}}