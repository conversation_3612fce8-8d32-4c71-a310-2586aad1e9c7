import * as vscode from 'vscode';
import { getWebviewContent } from './webview/webviewContent';
import { FileManager } from './services/fileManager';
import { LanguageModelService } from './services/languageModelService';
import { ChangeProcessor } from './services/changeProcessor';
import { Change, SessionConfig } from './types';

export class WakatimerProViewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'wakatimer-pro.mainView';
    private _view?: vscode.WebviewView;
    private fileManager: FileManager;
    private languageModelService: LanguageModelService;
    private changeProcessor: ChangeProcessor;
    private currentSession?: {
        config: SessionConfig;
        changes: Change[];
        isPaused: boolean;
        currentChangeIndex: number;
    };

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _context: vscode.ExtensionContext
    ) {
        this.fileManager = new FileManager();
        this.languageModelService = new LanguageModelService();
        this.changeProcessor = new ChangeProcessor();
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = getWebviewContent(webviewView.webview, this._extensionUri);

        webviewView.webview.onDidReceiveMessage(async (data) => {
            await this.handleMessage(data);
        });
    }

    public show() {
        if (this._view) {
            this._view.show?.(true);
        }
    }

    private async handleMessage(message: any) {
        try {
            switch (message.type) {
                case 'openFolder':
                    await this.handleOpenFolder();
                    break;
                case 'selectSourceFolder':
                    await this.handleSelectSourceFolder();
                    break;
                case 'copyFiles':
                    await this.handleCopyFiles(message.data);
                    break;
                case 'startSimpleMode':
                    await this.handleStartSimpleMode(message.data);
                    break;
                case 'generatePrompt':
                    await this.handleGeneratePrompt(message.data);
                    break;
                case 'processAIResponse':
                    await this.handleProcessAIResponse(message.data);
                    break;
                case 'previewChanges':
                    await this.handlePreviewChanges();
                    break;
                case 'startProcedure':
                    await this.handleStartProcedure();
                    break;
                case 'pauseProcedure':
                    this.handlePauseProcedure();
                    break;
                case 'resumeProcedure':
                    await this.handleResumeProcedure();
                    break;
                case 'checkFolderEmpty':
                    await this.handleCheckFolderEmpty();
                    break;
                case 'getProjectStructure':
                    await this.handleGetProjectStructure(message.data);
                    break;
                default:
                    console.warn('Unknown message type:', message.type);
            }
        } catch (error) {
            console.error('Error handling message:', error);
            this._view?.webview.postMessage({
                type: 'generalError',
                error: error instanceof Error ? error.message : 'An unexpected error occurred',
                messageType: message.type
            });
        }
    }

    private async handleOpenFolder() {
        const folderUri = await vscode.window.showOpenDialog({
            canSelectFolders: true,
            canSelectFiles: false,
            canSelectMany: false,
            openLabel: 'Select Folder'
        });

        if (folderUri && folderUri[0]) {
            await vscode.commands.executeCommand('vscode.openFolder', folderUri[0]);
        }
    }

    private async handleSelectSourceFolder() {
        const folderUri = await vscode.window.showOpenDialog({
            canSelectFolders: true,
            canSelectFiles: false,
            canSelectMany: false,
            openLabel: 'Select Source Folder'
        });

        if (folderUri && folderUri[0]) {
            const structure = await this.fileManager.getDirectoryStructure(folderUri[0].fsPath);
            this._view?.webview.postMessage({
                type: 'sourceStructure',
                data: {
                    path: folderUri[0].fsPath,
                    structure
                }
            });
        }
    }

    private async handleCopyFiles(data: any) {
        const { sourcePath, destinationPath, selectedFiles } = data;

        try {
            this._view?.webview.postMessage({
                type: 'copyProgress',
                stage: 'starting',
                message: 'Preparing to copy files...',
                progress: 0
            });

            // Copy files with progress updates
            for (let i = 0; i < selectedFiles.length; i++) {
                const filePath = selectedFiles[i];
                const progress = Math.round(((i + 1) / selectedFiles.length) * 100);

                this._view?.webview.postMessage({
                    type: 'copyProgress',
                    stage: 'copying',
                    message: `Copying file ${i + 1} of ${selectedFiles.length}: ${filePath}`,
                    progress
                });

                await this.fileManager.copyFiles(sourcePath, destinationPath, [filePath]);
            }

            this._view?.webview.postMessage({
                type: 'copyComplete',
                success: true
            });
        } catch (error) {
            this._view?.webview.postMessage({
                type: 'copyComplete',
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    private async handleCheckFolderEmpty() {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            const isEmpty = await this.fileManager.isFolderEmpty(workspaceFolder.uri.fsPath);
            this._view?.webview.postMessage({
                type: 'folderEmptyStatus',
                isEmpty,
                workspaceFolder: workspaceFolder.uri.fsPath
            });
        } else {
            this._view?.webview.postMessage({
                type: 'folderEmptyStatus',
                isEmpty: true,
                workspaceFolder: null
            });
        }
    }

    private async handleGetProjectStructure(data: any) {
        const { path } = data;
        const structure = await this.fileManager.getDirectoryStructure(path);
        this._view?.webview.postMessage({
            type: 'projectStructure',
            data: structure
        });
    }

    private async handleStartSimpleMode(data: any) {
        const { workingDirectory, days, hoursPerDay, dailyHours } = data;

        // Calculate total changes needed
        const totalHours = dailyHours ?
            Object.values(dailyHours).reduce((sum: number, hours: any) => sum + hours, 0) :
            days * hoursPerDay;
        const totalMinutes = totalHours * 60;
        const changesNeeded = Math.floor(totalMinutes / 1.5); // One change every 90 seconds

        this._view?.webview.postMessage({
            type: 'aiProgress',
            stage: 'analyzing',
            message: 'Analyzing project structure...'
        });

        try {
            // Get project structure
            const structure = await this.fileManager.getProjectStructure(workingDirectory);

            this._view?.webview.postMessage({
                type: 'aiProgress',
                stage: 'generating',
                message: 'Generating changes with AI...'
            });

            // Generate changes using AI
            const changes = await this.languageModelService.generateChanges(
                workingDirectory,
                structure,
                changesNeeded
            );

            this._view?.webview.postMessage({
                type: 'aiProgress',
                stage: 'verifying',
                message: 'Verifying changes...'
            });

            // Verify changes
            const verifiedChanges = await this.changeProcessor.verifyChanges(changes);

            // Store session data
            this.currentSession = {
                config: {
                    workingDirectory,
                    days,
                    hoursPerDay,
                    dailyHours,
                    mode: 'simple'
                },
                changes: verifiedChanges,
                isPaused: false,
                currentChangeIndex: 0
            };

            this._view?.webview.postMessage({
                type: 'aiProgress',
                stage: 'complete',
                message: 'Changes prepared successfully!'
            });

            // Small delay to show completion state
            setTimeout(() => {
                this._view?.webview.postMessage({
                    type: 'changesReady',
                    data: {
                        totalChanges: verifiedChanges.length,
                        changes: verifiedChanges.map(c => ({
                            file: c.file,
                            description: c.description,
                            lineCount: c.changes.length
                        }))
                    }
                });
            }, 1000);

        } catch (error) {
            this._view?.webview.postMessage({
                type: 'aiError',
                error: error instanceof Error ? error.message : 'Unknown error',
                canRetry: true
            });
        }
    }

    private async handleGeneratePrompt(data: any) {
        const { workingDirectory, days, hoursPerDay, dailyHours } = data;

        // Calculate total changes needed
        const totalHours = dailyHours ?
            Object.values(dailyHours).reduce((sum: number, hours: any) => sum + hours, 0) :
            days * hoursPerDay;
        const totalMinutes = totalHours * 60;
        const changesNeeded = Math.floor(totalMinutes / 1.5);

        try {
            const structure = await this.fileManager.getProjectStructure(workingDirectory);
            const prompt = this.languageModelService.generatePromptForAdvancedMode(
                workingDirectory,
                structure,
                changesNeeded
            );

            this._view?.webview.postMessage({
                type: 'promptGenerated',
                data: prompt
            });
        } catch (error) {
            this._view?.webview.postMessage({
                type: 'promptError',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    private async handleProcessAIResponse(data: any) {
        const { response, config } = data;

        try {
            // Parse the AI response
            const changes = this.languageModelService.parseAIResponse(response);

            // Verify changes
            const verifiedChanges = await this.changeProcessor.verifyChanges(changes);

            // Store session data
            this.currentSession = {
                config: {
                    ...config,
                    mode: 'advanced'
                },
                changes: verifiedChanges,
                isPaused: false,
                currentChangeIndex: 0
            };

            this._view?.webview.postMessage({
                type: 'changesReady',
                data: {
                    totalChanges: verifiedChanges.length,
                    changes: verifiedChanges.map(c => ({
                        file: c.file,
                        description: c.description,
                        lineCount: c.changes.length
                    }))
                }
            });

        } catch (error) {
            this._view?.webview.postMessage({
                type: 'parseError',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    private async handlePreviewChanges() {
        if (!this.currentSession) {
            return;
        }

        // Create a preview document
        const content = this.changeProcessor.generatePreview(this.currentSession.changes);
        const doc = await vscode.workspace.openTextDocument({
            content,
            language: 'diff'
        });
        await vscode.window.showTextDocument(doc, { preview: true });
    }

    private async handleStartProcedure() {
        if (!this.currentSession) {
            return;
        }

        this.currentSession.isPaused = false;
        await this.applyChanges();
    }

    private handlePauseProcedure() {
        if (this.currentSession) {
            this.currentSession.isPaused = true;
        }
    }

    private async handleResumeProcedure() {
        if (!this.currentSession) {
            return;
        }

        // Re-verify remaining changes
        const remainingChanges = this.currentSession.changes.slice(this.currentSession.currentChangeIndex);
        const verifiedChanges = await this.changeProcessor.verifyChanges(remainingChanges);

        // Update changes array
        this.currentSession.changes = [
            ...this.currentSession.changes.slice(0, this.currentSession.currentChangeIndex),
            ...verifiedChanges
        ];

        this.currentSession.isPaused = false;
        await this.applyChanges();
    }

    private async applyChanges() {
        if (!this.currentSession) {
            return;
        }

        const { changes, isPaused, currentChangeIndex } = this.currentSession;

        for (let i = currentChangeIndex; i < changes.length; i++) {
            if (isPaused) {
                break;
            }

            this.currentSession.currentChangeIndex = i;
            const change = changes[i];

            this._view?.webview.postMessage({
                type: 'changeProgress',
                data: {
                    current: i + 1,
                    total: changes.length,
                    currentFile: change.file,
                    description: change.description
                }
            });

            try {
                await this.changeProcessor.applyChange(change);

                // Wait 90 seconds before next change (or less for demo)
                if (i < changes.length - 1 && !isPaused) {
                    await this.delay(90000); // 90 seconds
                }
            } catch (error) {
                this._view?.webview.postMessage({
                    type: 'changeError',
                    error: error instanceof Error ? error.message : 'Unknown error',
                    changeIndex: i
                });
                this.currentSession.isPaused = true;
                break;
            }
        }

        if (this.currentSession.currentChangeIndex >= changes.length - 1) {
            this._view?.webview.postMessage({
                type: 'procedureComplete',
                data: {
                    totalChanges: changes.length,
                    duration: this.formatDuration(changes.length * 90)
                }
            });
        }
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    private formatDuration(seconds: number): string {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }
}
