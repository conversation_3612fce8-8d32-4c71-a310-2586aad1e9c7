import * as vscode from 'vscode';
import * as path from 'path';
import { FileNode, ProjectStructure } from '../types';

export class FileManager {
    async getDirectoryStructure(dirPath: string): Promise<FileNode> {
        const uri = vscode.Uri.file(dirPath);
        const name = path.basename(dirPath);
        
        const node: FileNode = {
            name,
            path: dirPath,
            type: 'directory',
            children: [],
            selected: true
        };

        try {
            const entries = await vscode.workspace.fs.readDirectory(uri);
            
            for (const [entryName, entryType] of entries) {
                const entryPath = path.join(dirPath, entryName);
                
                if (entryType === vscode.FileType.Directory) {
                    // Skip node_modules and hidden directories
                    if (entryName === 'node_modules' || entryName.startsWith('.')) {
                        continue;
                    }
                    const childNode = await this.getDirectoryStructure(entryPath);
                    node.children!.push(childNode);
                } else if (entryType === vscode.FileType.File) {
                    // Skip hidden files
                    if (entryName.startsWith('.')) {
                        continue;
                    }
                    node.children!.push({
                        name: entryName,
                        path: entryPath,
                        type: 'file',
                        selected: true
                    });
                }
            }
        } catch (error) {
            console.error(`Error reading directory ${dirPath}:`, error);
        }

        return node;
    }

    async getProjectStructure(rootPath: string): Promise<ProjectStructure> {
        const files: string[] = [];
        const directories: string[] = [];
        const tree = await this.getDirectoryStructure(rootPath);

        const traverse = (node: FileNode) => {
            if (node.type === 'directory') {
                directories.push(node.path);
                node.children?.forEach(child => traverse(child));
            } else {
                files.push(node.path);
            }
        };

        traverse(tree);

        return { files, directories, tree };
    }

    async isFolderEmpty(folderPath: string): Promise<boolean> {
        const uri = vscode.Uri.file(folderPath);
        try {
            const entries = await vscode.workspace.fs.readDirectory(uri);
            return entries.length === 0;
        } catch (error) {
            return true;
        }
    }

    async copyFiles(sourcePath: string, destinationPath: string, selectedFiles: string[]): Promise<void> {
        for (const filePath of selectedFiles) {
            const relativePath = path.relative(sourcePath, filePath);
            const destPath = path.join(destinationPath, relativePath);
            const destDir = path.dirname(destPath);

            // Create destination directory if it doesn't exist
            await vscode.workspace.fs.createDirectory(vscode.Uri.file(destDir));

            // Copy the file
            const sourceUri = vscode.Uri.file(filePath);
            const destUri = vscode.Uri.file(destPath);
            
            try {
                const content = await vscode.workspace.fs.readFile(sourceUri);
                await vscode.workspace.fs.writeFile(destUri, content);
            } catch (error) {
                console.error(`Error copying file ${filePath}:`, error);
                throw error;
            }
        }
    }

    async readFileContent(filePath: string): Promise<string> {
        const uri = vscode.Uri.file(filePath);
        const content = await vscode.workspace.fs.readFile(uri);
        return Buffer.from(content).toString('utf8');
    }

    async readFileWithLineNumbers(filePath: string): Promise<string> {
        const content = await this.readFileContent(filePath);
        const lines = content.split('\n');
        return lines.map((line, index) => `${index + 1}|${line}`).join('\n');
    }

    async writeFile(filePath: string, content: string): Promise<void> {
        const uri = vscode.Uri.file(filePath);
        const buffer = Buffer.from(content, 'utf8');
        await vscode.workspace.fs.writeFile(uri, buffer);
    }
}
