import * as vscode from 'vscode';
import * as path from 'path';
import { FileNode, ProjectStructure } from '../types';

export class FileManager {
    async getDirectoryStructure(dirPath: string): Promise<FileNode> {
        const uri = vscode.Uri.file(dirPath);
        const name = path.basename(dirPath);

        const node: FileNode = {
            name,
            path: dirPath,
            type: 'directory',
            children: [],
            selected: true
        };

        try {
            const entries = await vscode.workspace.fs.readDirectory(uri);

            for (const [entryName, entryType] of entries) {
                const entryPath = path.join(dirPath, entryName);

                if (entryType === vscode.FileType.Directory) {
                    // Skip node_modules and hidden directories
                    if (entryName === 'node_modules' || entryName.startsWith('.')) {
                        continue;
                    }
                    const childNode = await this.getDirectoryStructure(entryPath);
                    node.children!.push(childNode);
                } else if (entryType === vscode.FileType.File) {
                    // Skip hidden files
                    if (entryName.startsWith('.')) {
                        continue;
                    }
                    node.children!.push({
                        name: entryName,
                        path: entryPath,
                        type: 'file',
                        selected: true
                    });
                }
            }
        } catch (error) {
            console.error(`Error reading directory ${dirPath}:`, error);
        }

        return node;
    }

    async getProjectStructure(rootPath: string): Promise<ProjectStructure> {
        const files: string[] = [];
        const directories: string[] = [];
        const tree = await this.getDirectoryStructure(rootPath);

        const traverse = (node: FileNode) => {
            if (node.type === 'directory') {
                directories.push(node.path);
                node.children?.forEach(child => traverse(child));
            } else {
                files.push(node.path);
            }
        };

        traverse(tree);

        return { files, directories, tree };
    }

    async isFolderEmpty(folderPath: string): Promise<boolean> {
        const uri = vscode.Uri.file(folderPath);
        try {
            const entries = await vscode.workspace.fs.readDirectory(uri);
            return entries.length === 0;
        } catch (error) {
            return true;
        }
    }

    async copyFiles(sourcePath: string, destinationPath: string, selectedFiles: string[]): Promise<void> {
        if (!selectedFiles || selectedFiles.length === 0) {
            throw new Error('No files selected for copying');
        }

        if (!sourcePath || !destinationPath) {
            throw new Error('Source and destination paths are required');
        }

        for (const filePath of selectedFiles) {
            try {
                const relativePath = path.relative(sourcePath, filePath);
                const destPath = path.join(destinationPath, relativePath);
                const destDir = path.dirname(destPath);

                // Validate file exists
                const sourceUri = vscode.Uri.file(filePath);
                try {
                    await vscode.workspace.fs.stat(sourceUri);
                } catch (error) {
                    throw new Error(`Source file does not exist: ${filePath}`);
                }

                // Create destination directory if it doesn't exist
                try {
                    await vscode.workspace.fs.createDirectory(vscode.Uri.file(destDir));
                } catch (error) {
                    throw new Error(`Failed to create destination directory: ${destDir}`);
                }

                // Copy the file
                const destUri = vscode.Uri.file(destPath);

                try {
                    const content = await vscode.workspace.fs.readFile(sourceUri);
                    await vscode.workspace.fs.writeFile(destUri, content);
                } catch (error) {
                    throw new Error(`Failed to copy file from ${filePath} to ${destPath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            } catch (error) {
                console.error(`Error copying file ${filePath}:`, error);
                throw error;
            }
        }
    }

    async readFileContent(filePath: string): Promise<string> {
        if (!filePath) {
            throw new Error('File path is required');
        }

        try {
            const uri = vscode.Uri.file(filePath);

            // Check if file exists first
            try {
                await vscode.workspace.fs.stat(uri);
            } catch (error) {
                throw new Error(`File does not exist: ${filePath}`);
            }

            const content = await vscode.workspace.fs.readFile(uri);
            return Buffer.from(content).toString('utf8');
        } catch (error) {
            if (error instanceof Error && error.message.includes('File does not exist')) {
                throw error;
            }
            throw new Error(`Failed to read file ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    async readFileWithLineNumbers(filePath: string): Promise<string> {
        try {
            const content = await this.readFileContent(filePath);
            const lines = content.split('\n');
            return lines.map((line, index) => `${index + 1}|${line}`).join('\n');
        } catch (error) {
            throw new Error(`Failed to read file with line numbers ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    async writeFile(filePath: string, content: string): Promise<void> {
        if (!filePath) {
            throw new Error('File path is required');
        }

        if (content === undefined || content === null) {
            throw new Error('File content is required');
        }

        try {
            const uri = vscode.Uri.file(filePath);
            const buffer = Buffer.from(content, 'utf8');
            await vscode.workspace.fs.writeFile(uri, buffer);
        } catch (error) {
            throw new Error(`Failed to write file ${filePath}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
