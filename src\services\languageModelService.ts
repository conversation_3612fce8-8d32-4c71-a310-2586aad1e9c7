import * as vscode from 'vscode';
import { Change, LineChange, ProjectStructure } from '../types';
import { FileManager } from './fileManager';
import * as path from 'path';

export class LanguageModelService {
    private fileManager: FileManager;

    constructor() {
        this.fileManager = new FileManager();
    }

    async generateChanges(
        workingDirectory: string,
        structure: ProjectStructure,
        changesNeeded: number
    ): Promise<Change[]> {
        // Input validation
        if (!workingDirectory) {
            throw new Error('Working directory is required');
        }

        if (!structure || !structure.files || structure.files.length === 0) {
            throw new Error('Project structure is required and must contain files');
        }

        if (changesNeeded <= 0) {
            throw new Error('Number of changes needed must be greater than 0');
        }
        // Try to select the best available model
        let models = await vscode.lm.selectChatModels({
            vendor: 'copilot',
            family: 'gpt-4.1'
        });

        // Fallback to any GPT-4 model if 4.1 is not available
        if (models.length === 0) {
            models = await vscode.lm.selectChatModels({
                vendor: 'copilot',
                family: 'gpt-4'
            });
        }

        // Fallback to any Copilot model
        if (models.length === 0) {
            models = await vscode.lm.selectChatModels({
                vendor: 'copilot'
            });
        }

        // Final fallback to any available model
        if (models.length === 0) {
            models = await vscode.lm.selectChatModels();
        }

        if (models.length === 0) {
            throw new Error('No language model available. Please ensure GitHub Copilot or another compatible AI extension is installed and activated.');
        }

        const model = models[0];
        console.log('Selected AI model:', model?.id || model?.name || model);
        // Build the initial prompt
        const structureDescription = this.describeProjectStructure(structure);
        const prompt = this.buildInitialPrompt(structureDescription, changesNeeded);
        console.log('Initial prompt being sent to AI:', prompt);
        const messages = [
            vscode.LanguageModelChatMessage.User(prompt)
        ];

        try {
            // Create cancellation token with timeout
            const cancellationTokenSource = new vscode.CancellationTokenSource();
            const timeoutId = setTimeout(() => {
                cancellationTokenSource.cancel();
            }, 120000); // 2 minute timeout

            try {
                // First request - AI will analyze and request file contents
                const analysisResponse = await model.sendRequest(
                    messages,
                    {},
                    cancellationTokenSource.token
                );

                clearTimeout(timeoutId);

                let analysisText = '';
                for await (const fragment of analysisResponse.text) {
                    analysisText += fragment;
                }
                console.log('Full analysis response from AI:', analysisText);

                // Parse file read requests from AI response
                const fileRequests = this.parseFileRequests(analysisText);

                // Read requested files
                const fileContents: Record<string, string> = {};
                for (const filePath of fileRequests) {
                    // If the filePath is absolute, use as-is. If relative, join with workingDirectory.
                    let fullPath = filePath;
                    if (!path.isAbsolute(filePath)) {
                        fullPath = vscode.Uri.joinPath(vscode.Uri.file(workingDirectory), filePath).fsPath;
                    }
                    try {
                        fileContents[filePath] = await this.fileManager.readFileWithLineNumbers(fullPath);
                    } catch (error) {
                        console.error(`Error reading file ${filePath}:`, error);
                    }
                }

                // Second request - provide file contents and get changes
                messages.push(vscode.LanguageModelChatMessage.Assistant(analysisText));
                const fileContentsPrompt = this.buildFileContentsResponse(fileContents);
                messages.push(vscode.LanguageModelChatMessage.User(fileContentsPrompt));
                console.log('Prompt with file contents being sent to AI:', fileContentsPrompt);

                const changesResponse = await model.sendRequest(
                    messages,
                    {},
                    new vscode.CancellationTokenSource().token
                );

                let changesText = '';
                for await (const fragment of changesResponse.text) {
                    changesText += fragment;
                }
                console.log('Full changes response from AI:', changesText);

                // Parse the changes from AI response
                return this.parseChangesFromResponse(changesText, workingDirectory);

            } catch (timeoutError) {
                clearTimeout(timeoutId);
                if (cancellationTokenSource.token.isCancellationRequested) {
                    throw new Error('AI request timed out after 2 minutes. Please try again or check your internet connection.');
                }
                throw timeoutError;
            }

        } catch (err) {
            if (err instanceof vscode.LanguageModelError) {
                console.error('Language Model Error:', err.message, err.code, err.cause);
                if (err.message.includes('rate limit')) {
                    throw new Error('AI service rate limit exceeded. Please wait a moment and try again.');
                } else if (err.message.includes('authentication')) {
                    throw new Error('AI service authentication failed. Please check your GitHub Copilot subscription.');
                } else {
                    throw new Error(`AI Model Error: ${err.message}`);
                }
            }
            throw err;
        }
    }

    private buildInitialPrompt(structureDescription: string, changesNeeded: number): string {
        return `You are helping to generate retroactive time tracking data by creating ${changesNeeded} meaningful code changes across a project.

Project Structure:
${structureDescription}

Instructions:
1. First, analyze the project structure and request to read relevant files using <read_file> tags
2. After receiving file contents, generate exactly ${changesNeeded} changes distributed across different files
3. Changes should be realistic improvements: refactoring, adding comments, improving code style, adding error handling, etc.
4. Each change should modify 1-5 lines of code
5. Distribute changes evenly across multiple files

To request file contents, use:
<read_file>path/to/file.ext</read_file>

Respond with the files you need to read to generate meaningful changes.`;
    }

    private buildFileContentsResponse(fileContents: Record<string, string>): string {
        let response = 'Here are the requested file contents:\n\n';

        for (const [filePath, content] of Object.entries(fileContents)) {
            response += `<file path="${filePath}">\n${content}\n</file>\n\n`;
        }

        response += `\nNow generate the changes using this format:\n
<changes>
  <change>
    <file>path/to/file.ext</file>
    <description>Brief description of the change</description>
    <line_changes>
      <line_change>
        <line_number>5</line_number>
        <old_content>    const data = getData();</old_content>
        <new_content>    // Fetch data from the API\n    const data = getData();</new_content>
      </line_change>
    </line_changes>
  </change>
</changes>`;

        return response;
    }

    private describeProjectStructure(structure: ProjectStructure): string {
        const lines: string[] = [];

        // Add directory count
        lines.push(`Directories: ${structure.directories.length}`);

        // Add file breakdown by type
        const fileTypes: Record<string, number> = {};
        for (const file of structure.files) {
            const ext = file.split('.').pop() || 'no-extension';
            fileTypes[ext] = (fileTypes[ext] || 0) + 1;
        }

        lines.push('\nFiles by type:');
        for (const [ext, count] of Object.entries(fileTypes)) {
            lines.push(`  .${ext}: ${count} files`);
        }

        // Add file list (limited to prevent overwhelming the model)
        lines.push('\nKey files:');
        const relevantFiles = structure.files
            .filter(f => !f.includes('node_modules') && !f.includes('.git'))
            .slice(0, 20);

        for (const file of relevantFiles) {
            lines.push(`  ${file}`);
        }

        if (structure.files.length > 20) {
            lines.push(`  ... and ${structure.files.length - 20} more files`);
        }

        return lines.join('\n');
    }

    private parseFileRequests(response: string): string[] {
        const fileRequests: string[] = [];
        const regex = /<read_file>([^<]+)<\/read_file>/g;
        let match;

        while ((match = regex.exec(response)) !== null) {
            fileRequests.push(match[1].trim());
        }

        return fileRequests;
    }

    private parseChangesFromResponse(response: string, workingDirectory: string): Change[] {
        const changes: Change[] = [];

        // Extract changes XML
        const changesMatch = response.match(/<changes>([\s\S]*?)<\/changes>/i);
        if (!changesMatch) {
            throw new Error('No changes found in AI response');
        }

        const changesXml = changesMatch[1];
        const changeRegex = /<change>([\s\S]*?)<\/change>/gi;
        let changeMatch;

        while ((changeMatch = changeRegex.exec(changesXml)) !== null) {
            const changeContent = changeMatch[1];

            // Parse individual change
            const fileMatch = changeContent.match(/<file>([^<]+)<\/file>/i);
            const descMatch = changeContent.match(/<description>([^<]+)<\/description>/i);

            if (!fileMatch || !descMatch) {
                continue;
            }

            let filePathRaw = fileMatch[1].trim();
            let file: string;
            if (path.isAbsolute(filePathRaw)) {
                file = filePathRaw;
            } else {
                file = vscode.Uri.joinPath(vscode.Uri.file(workingDirectory), filePathRaw).fsPath;
            }
            const description = descMatch[1].trim();

            // Parse line changes
            const lineChanges: LineChange[] = [];
            const lineChangeRegex = /<line_change>([\s\S]*?)<\/line_change>/gi;
            let lineChangeMatch;

            while ((lineChangeMatch = lineChangeRegex.exec(changeContent)) !== null) {
                const lineChangeContent = lineChangeMatch[1];

                const lineNumMatch = lineChangeContent.match(/<line_number>([\d]+)<\/line_number>/i);
                const oldMatch = lineChangeContent.match(/<old_content>([\s\S]*?)<\/old_content>/i);
                const newMatch = lineChangeContent.match(/<new_content>([\s\S]*?)<\/new_content>/i);

                if (lineNumMatch && oldMatch && newMatch) {
                    lineChanges.push({
                        lineNumber: parseInt(lineNumMatch[1]),
                        oldContent: oldMatch[1].trim(),
                        newContent: newMatch[1].trim()
                    });
                }
            }

            if (lineChanges.length > 0) {
                changes.push({ file, description, changes: lineChanges });
            }
        }

        return changes;
    }

    generatePromptForAdvancedMode(
        workingDirectory: string,
        structure: ProjectStructure,
        changesNeeded: number
    ): string {
        const structureDescription = this.describeProjectStructure(structure);

        return `Generate ${changesNeeded} code changes for retroactive time tracking.

Project Location: ${workingDirectory}

Project Structure:
${structureDescription}

Instructions:
1. Generate exactly ${changesNeeded} meaningful code changes
2. Changes should be improvements: refactoring, comments, error handling, code style, etc.
3. Each change should modify 1-5 lines of code
4. Distribute changes across multiple files
5. Use the following XML format for your response:

<changes>
  <change>
    <file>relative/path/to/file.ext</file>
    <description>Brief description of what changed</description>
    <line_changes>
      <line_change>
        <line_number>5</line_number>
        <old_content>original line content</old_content>
        <new_content>new line content</new_content>
      </line_change>
    </line_changes>
  </change>
  <!-- Repeat for all ${changesNeeded} changes -->
</changes>

IMPORTANT:
- Line numbers must be accurate
- Old content must match exactly what's in the file
- Changes should be realistic and improve code quality
- Distribute changes across different files`;
    }

    parseAIResponse(response: string): Change[] {
        // For advanced mode, parse the response assuming it follows the XML format
        try {
            // Use the same parser as for the automatic mode
            return this.parseChangesFromResponse(response, '');
        } catch (error) {
            throw new Error(`Failed to parse AI response: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
